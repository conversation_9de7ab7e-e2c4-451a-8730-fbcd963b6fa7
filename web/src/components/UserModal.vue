<template>
  <!-- User Create/Edit Modal -->
  <UModal v-model="isOpen" :ui="{ width: 'sm:max-w-md' }">
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ isEditing ? '编辑用户' : '创建用户' }}
          </h3>
        </div>
      </template>

      <UForm
        :schema="formSchema"
        :state="localForm"
        @submit="handleSubmit"
        class="space-y-4"
      >
        <UFormGroup label="用户名" name="username" :error="getFieldError('username')">
          <UInput
            v-model="localForm.username"
            :disabled="isEditing"
            placeholder="请输入用户名"
            @blur="validateField('username', localForm.username)"
          />
        </UFormGroup>

        <UFormGroup label="邮箱" name="email" :error="getFieldError('email')">
          <UInput
            v-model="localForm.email"
            type="email"
            placeholder="请输入邮箱地址"
            @blur="validateField('email', localForm.email)"
          />
        </UFormGroup>

        <div class="grid grid-cols-2 gap-4">
          <UFormGroup label="名" name="first_name" :error="getFieldError('first_name')">
            <UInput
              v-model="localForm.first_name"
              placeholder="请输入名"
              @blur="validateField('first_name', localForm.first_name)"
            />
          </UFormGroup>

          <UFormGroup label="姓" name="last_name" :error="getFieldError('last_name')">
            <UInput
              v-model="localForm.last_name"
              placeholder="请输入姓"
              @blur="validateField('last_name', localForm.last_name)"
            />
          </UFormGroup>
        </div>

        <UFormGroup
          v-if="!isEditing"
          label="密码"
          name="password"
          :error="getFieldError('password')"
          help="密码至少8位，包含大小写字母、数字和特殊字符"
        >
          <UInput
            v-model="localForm.password"
            type="password"
            placeholder="请输入密码"
            @blur="validateField('password', localForm.password)"
          />
        </UFormGroup>

        <UFormGroup
          label="账户状态"
          name="is_active"
          :help="isSuperAdminEditingSelf ? '超级管理员不能停用自己的账户' : undefined"
        >
          <UCheckbox
            v-model="localForm.is_active"
            :disabled="isSuperAdminEditingSelf"
            label="激活账户"
          />
        </UFormGroup>
      </UForm>

      <template #footer>
        <div class="flex justify-end space-x-3">
          <UButton
            color="gray"
            variant="ghost"
            @click="handleClose"
          >
            取消
          </UButton>
          <UButton
            :loading="loading"
            @click="handleSubmit"
          >
            {{ isEditing ? '更新' : '创建' }}
          </UButton>
        </div>
      </template>
    </UCard>
  </UModal>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useAuthStore } from '../stores/auth'
import { useFormValidation } from '../composables/useFormValidation'
import { createUserSchema, updateUserSchema, type CreateUserForm, type UpdateUserForm } from '../schemas/user'

interface Props {
  show: boolean
  form: CreateUserForm | UpdateUserForm
  isEditing: boolean
  loading?: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'submit', data: CreateUserForm | UpdateUserForm): void
  (e: 'update:form', value: CreateUserForm | UpdateUserForm): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const authStore = useAuthStore()

// 响应式的模态框开关状态
const isOpen = computed({
  get: () => props.show,
  set: (value) => {
    if (!value) {
      emit('close')
    }
  }
})

// 表单数据
const localForm = computed({
  get: () => props.form,
  set: (value) => emit('update:form', value)
})

// 根据编辑状态选择验证模式
const formSchema = computed(() => {
  return props.isEditing ? updateUserSchema : createUserSchema
})

// 表单验证
const { validate, validateField, getFieldError, clearErrors } = useFormValidation(formSchema.value)

// 检查是否是超级管理员在编辑自己
const isSuperAdminEditingSelf = computed(() => {
  return props.isEditing &&
         authStore.user?.username === 'admin' &&
         authStore.user?.id === (props.form as UpdateUserForm).id
})

// 处理表单提交
const handleSubmit = async () => {
  const isValid = await validate(localForm.value)
  if (isValid) {
    emit('submit', localForm.value)
  }
}

// 处理关闭
const handleClose = () => {
  clearErrors()
  emit('close')
}

// 监听show属性变化，清除错误
watch(() => props.show, (newShow) => {
  if (newShow) {
    clearErrors()
  }
})
</script>
