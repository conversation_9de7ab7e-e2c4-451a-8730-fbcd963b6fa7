import { ref, reactive } from 'vue'

export interface Notification {
  id: string
  title: string
  description?: string
  color: 'primary' | 'red' | 'orange' | 'amber' | 'yellow' | 'lime' | 'green' | 'emerald' | 'teal' | 'cyan' | 'sky' | 'blue' | 'indigo' | 'violet' | 'purple' | 'fuchsia' | 'pink' | 'rose' | 'gray'
  timeout?: number
  actions?: Array<{
    label: string
    click: () => void
  }>
}

const notifications = ref<Notification[]>([])

export function useNotifications() {
  const add = (notification: Omit<Notification, 'id'>) => {
    const id = Math.random().toString(36).substring(2, 9)
    const newNotification: Notification = {
      id,
      ...notification
    }
    
    notifications.value.push(newNotification)
    
    // 自动移除通知
    if (notification.timeout !== 0) {
      setTimeout(() => {
        remove(id)
      }, notification.timeout || 5000)
    }
    
    return id
  }

  const remove = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  const clear = () => {
    notifications.value = []
  }

  // 便捷方法
  const success = (title: string, description?: string, timeout?: number) => {
    return add({
      title,
      description,
      color: 'green',
      timeout
    })
  }

  const error = (title: string, description?: string, timeout?: number) => {
    return add({
      title,
      description,
      color: 'red',
      timeout
    })
  }

  const warning = (title: string, description?: string, timeout?: number) => {
    return add({
      title,
      description,
      color: 'orange',
      timeout
    })
  }

  const info = (title: string, description?: string, timeout?: number) => {
    return add({
      title,
      description,
      color: 'blue',
      timeout
    })
  }

  return {
    notifications,
    add,
    remove,
    clear,
    success,
    error,
    warning,
    info
  }
}
