<template>
  <Layout>
    <div>
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-xl font-semibold text-gray-900">用户</h1>
          <p class="mt-2 text-sm text-gray-700">
            系统中所有用户的列表，包括姓名、邮箱和角色。
          </p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <button
            v-if="authStore.hasPermission('users.create')"
            @click="openCreateModal"
            type="button"
            class="btn btn-primary"
          >
            添加用户
          </button>
        </div>
      </div>

      <!-- Users Table -->
      <UserList
        :users="usersStore.users"
        :can-edit="authStore.hasPermission('users.update')"
        :can-delete="authStore.hasPermission('users.delete')"
        @edit="editUser"
        @delete="deleteUser"
      />

      <!-- Pagination -->
      <UserPagination
        v-if="usersStore.total > 0"
        :current-page="usersStore.currentPage"
        :total-pages="Math.ceil(usersStore.total / usersStore.pageSize)"
        :total="usersStore.total"
        :per-page="usersStore.pageSize"
        @previous="usersStore.previousPage()"
        @next="usersStore.nextPage()"
        @goto="usersStore.goToPage"
      />
    </div>

    <!-- Create/Edit User Modal -->
    <UserModal
      :show="showUserModal"
      v-model:form="userForm"
      :is-editing="!!editingUser"
      :loading="usersStore.loading"
      @close="closeModal"
      @submit="submitUser"
    />


  </Layout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import Layout from '../components/Layout.vue'
import UserList from '../components/UserList.vue'
import UserModal from '../components/UserModal.vue'
import UserPagination from '../components/UserPagination.vue'
import { useAuthStore } from '../stores/auth'
import { useUsersStore } from '../stores/users'
import { type CreateUserForm, type UpdateUserForm } from '../schemas/user'

const authStore = useAuthStore()
const usersStore = useUsersStore()
const showUserModal = ref(false)
const editingUser = ref<any>(null)

const userForm = reactive<CreateUserForm | UpdateUserForm>({
  username: '',
  email: '',
  password: '',
  first_name: '',
  last_name: '',
  is_active: true
})

const openCreateModal = () => {
  editingUser.value = null
  resetForm()
  showUserModal.value = true
}

const editUser = (user: any) => {
  editingUser.value = user
  // 为编辑模式设置表单数据
  Object.assign(userForm, {
    id: user.id,
    username: user.username,
    email: user.email,
    first_name: user.first_name,
    last_name: user.last_name,
    is_active: user.is_active,
    password: '' // 编辑时不填充密码
  })
  showUserModal.value = true
}

const closeModal = () => {
  showUserModal.value = false
  editingUser.value = null
  resetForm()
}

const resetForm = () => {
  Object.assign(userForm, {
    username: '',
    email: '',
    password: '',
    first_name: '',
    last_name: '',
    is_active: true
  })
  // 如果有id字段，也要清除
  if ('id' in userForm) {
    delete (userForm as any).id
  }
}

const submitUser = async (formData: CreateUserForm | UpdateUserForm) => {
  try {
    if (editingUser.value) {
      // Update existing user
      await usersStore.updateUser(editingUser.value.id, formData as UpdateUserForm)
    } else {
      // Create new user
      await usersStore.createUser(formData as CreateUserForm)
    }

    closeModal()
    await usersStore.fetchUsers()
  } catch (error) {
    console.error('Error saving user:', error)
  }
}

const deleteUser = async (user: any) => {
  if (confirm(`确定要删除用户 ${user.first_name} ${user.last_name} 吗？`)) {
    try {
      await usersStore.deleteUser(user.id)
      await usersStore.fetchUsers()
    } catch (error) {
      console.error('Error deleting user:', error)
    }
  }
}



onMounted(() => {
  usersStore.fetchUsers()
})
</script>
