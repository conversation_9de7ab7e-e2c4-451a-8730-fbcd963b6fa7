<template>
  <div class="flex flex-col h-full bg-gray-900">
    <!-- Logo/Brand -->
    <router-link to="/" class="flex items-center justify-center h-16 px-4 bg-gray-800 hover:bg-gray-700 transition-colors duration-200">
      <h1 class="text-xl font-bold text-white">管理系统</h1>
    </router-link>

    <!-- Navigation -->
    <nav class="flex-1 px-4 py-6 space-y-6">
      <!-- Main Section -->
      <div>
        <h3 class="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
          主要功能
        </h3>
        <div class="space-y-1">
          <router-link
            to="/"
            class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200"
            :class="isActive('/') ? 'bg-gray-700 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'"
          >
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z" />
            </svg>
            仪表板
          </router-link>
        </div>
      </div>

      <!-- User Management Section -->
      <div v-if="authStore.hasPermission('users.read') || authStore.hasPermission('roles.read')">
        <h3 class="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
          用户管理
        </h3>
        <div class="space-y-1">
          <router-link
            v-if="authStore.hasPermission('users.read')"
            to="/users"
            class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200"
            :class="isActive('/users') ? 'bg-gray-700 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'"
          >
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
            用户
          </router-link>

          <router-link
            v-if="authStore.hasPermission('roles.read')"
            to="/roles"
            class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200"
            :class="isActive('/roles') ? 'bg-gray-700 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'"
          >
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
            角色
          </router-link>

          <router-link
            v-if="authStore.hasPermission('roles.update')"
            to="/permissions"
            class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200"
            :class="isActive('/permissions') ? 'bg-gray-700 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'"
          >
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
            权限
          </router-link>
        </div>
      </div>

      <!-- Account Section -->
      <div>
        <h3 class="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
          账户
        </h3>
        <div class="space-y-1">
          <router-link
            to="/profile"
            class="flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200"
            :class="isActive('/profile') ? 'bg-gray-700 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'"
          >
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            个人资料
          </router-link>
        </div>
      </div>
    </nav>

    <!-- User Info & Logout -->
    <div class="px-4 py-4 border-t border-gray-700">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
            <span class="text-sm font-medium text-white">
              {{ userInitials }}
            </span>
          </div>
        </div>
        <div class="ml-3 flex-1 min-w-0">
          <p class="text-sm font-medium text-white truncate">
            {{ authStore.user?.first_name }} {{ authStore.user?.last_name }}
          </p>
          <p class="text-xs text-gray-400 truncate">
            {{ authStore.user?.email }}
          </p>
        </div>
        <div class="ml-3">
          <button
            @click="logout"
            class="flex items-center justify-center w-8 h-8 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200"
            title="退出登录"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const userInitials = computed(() => {
  if (!authStore.user) return 'U'
  const firstName = authStore.user.first_name || ''
  const lastName = authStore.user.last_name || ''
  return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase() || 'U'
})

const isActive = (path: string) => {
  if (path === '/') {
    return route.path === '/'
  }
  return route.path.startsWith(path)
}

const logout = () => {
  authStore.logout()
  router.push('/login')
}
</script>
