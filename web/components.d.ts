/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    GlobalNotifications: typeof import('./src/components/GlobalNotifications.vue')['default']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    Layout: typeof import('./src/components/Layout.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Sidebar: typeof import('./src/components/Sidebar.vue')['default']
    UAlert: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/components/Alert.vue')['default']
    UApp: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/components/App.vue')['default']
    UButton: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/components/Button.vue')['default']
    UCard: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/components/Card.vue')['default']
    UCheckbox: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/components/Checkbox.vue')['default']
    UForm: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/components/Form.vue')['default']
    UInput: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/components/Input.vue')['default']
    UModal: typeof import('./node_modules/.pnpm/@nuxt+ui@3.3.0_@babel+parser@7.28.0_axios@1.11.0_embla-carousel@8.6.0_typescript@5.8.3__5ab1c236ad006bdc63184ce4e1689f55/node_modules/@nuxt/ui/dist/runtime/components/Modal.vue')['default']
    UserList: typeof import('./src/components/UserList.vue')['default']
    UserModal: typeof import('./src/components/UserModal.vue')['default']
    UserPagination: typeof import('./src/components/UserPagination.vue')['default']
  }
}
