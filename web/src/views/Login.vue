<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div class="text-center">
        <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
          登录您的账户
        </h2>
        <p class="mt-2 text-sm text-gray-600">
          管理系统
        </p>
      </div>

      <UCard class="mt-8">
        <UAlert
          v-if="authStore.error"
          color="red"
          variant="soft"
          :title="authStore.error"
          class="mb-6"
        />

        <UForm
          :schema="loginSchema"
          :state="form"
          @submit="handleLogin"
          class="space-y-6"
        >
          <UFormGroup
            label="用户名或邮箱"
            name="identifier"
            :error="getFieldError('identifier')"
          >
            <UInput
              v-model="form.identifier"
              placeholder="请输入用户名或邮箱"
              @blur="validateField('identifier', form.identifier)"
            />
          </UFormGroup>

          <UFormGroup
            label="密码"
            name="password"
            :error="getFieldError('password')"
          >
            <UInput
              v-model="form.password"
              type="password"
              placeholder="请输入密码"
              @blur="validateField('password', form.password)"
            />
          </UFormGroup>

          <UButton
            type="submit"
            :loading="authStore.loading"
            block
            size="lg"
          >
            登录
          </UButton>
        </UForm>
      </UCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useFormValidation } from '../composables/useFormValidation'
import { loginSchema, type LoginForm } from '../schemas/user'

const router = useRouter()
const authStore = useAuthStore()

const form = reactive<LoginForm>({
  identifier: '',
  password: ''
})

// 表单验证
const { validate, validateField, getFieldError, clearErrors } = useFormValidation(loginSchema)

const handleLogin = async () => {
  // 先进行前端验证
  const isValid = await validate(form)
  if (!isValid) {
    return
  }

  // 清除之前的错误
  clearErrors()

  // 调用登录API
  const result = await authStore.login(form)
  if (result.success) {
    router.push('/')
  }
}
</script>
