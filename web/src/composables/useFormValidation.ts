import { computed, ref, type Ref } from 'vue'
import { z, type ZodSchema } from 'zod'

export interface ValidationError {
  field: string
  message: string
}

export interface UseFormValidationReturn<T> {
  errors: Ref<ValidationError[]>
  isValid: Ref<boolean>
  validate: (data: T) => Promise<boolean>
  validateField: (field: keyof T, value: any) => Promise<boolean>
  clearErrors: () => void
  clearFieldError: (field: keyof T) => void
  getFieldError: (field: keyof T) => string | undefined
  hasFieldError: (field: keyof T) => boolean
}

export function useFormValidation<T>(schema: ZodSchema<T>): UseFormValidationReturn<T> {
  const errors = ref<ValidationError[]>([])

  const isValid = computed(() => errors.value.length === 0)

  const validate = async (data: T): Promise<boolean> => {
    try {
      await schema.parseAsync(data)
      errors.value = []
      return true
    } catch (error) {
      if (error instanceof z.ZodError) {
        errors.value = error.issues.map((err: any) => ({
          field: err.path.join('.'),
          message: err.message
        }))
      }
      return false
    }
  }

  const validateField = async (field: keyof T, value: any): Promise<boolean> => {
    try {
      // 在Zod 4中，我们需要直接验证单个字段值
      // 由于无法轻易从复合schema中提取单个字段的schema，
      // 我们改为验证整个对象，但只关注特定字段的错误
      const testData = { [field]: value } as Partial<T>

      // 尝试解析，这会抛出包含所有字段错误的ZodError
      await schema.parseAsync(testData as T)

      // 如果没有抛出错误，清除该字段的错误
      clearFieldError(field)
      return true
    } catch (error) {
      if (error instanceof z.ZodError) {
        // 移除该字段的旧错误
        clearFieldError(field)

        // 只添加与当前字段相关的错误
        const fieldErrors = error.issues
          .filter((err: any) => err.path.length > 0 && err.path[0] === field)
          .map((err: any) => ({
            field: err.path.join('.'),
            message: err.message
          }))

        errors.value.push(...fieldErrors)
        return fieldErrors.length === 0
      }
      return false
    }
  }

  const clearErrors = () => {
    errors.value = []
  }

  const clearFieldError = (field: keyof T) => {
    errors.value = errors.value.filter(error => error.field !== field)
  }

  const getFieldError = (field: keyof T): string | undefined => {
    const error = errors.value.find(error => error.field === field)
    return error?.message
  }

  const hasFieldError = (field: keyof T): boolean => {
    return errors.value.some(error => error.field === field)
  }

  return {
    errors,
    isValid,
    validate,
    validateField,
    clearErrors,
    clearFieldError,
    getFieldError,
    hasFieldError
  }
}

// 专门用于处理API错误的函数
export function handleApiValidationError(error: any, setErrors: (errors: ValidationError[]) => void) {
  if (error.response?.data?.errors) {
    // 处理后端返回的验证错误
    const apiErrors: ValidationError[] = Object.entries(error.response.data.errors).map(([field, messages]) => ({
      field,
      message: Array.isArray(messages) ? messages[0] : messages as string
    }))
    setErrors(apiErrors)
  } else if (error.response?.data?.message) {
    // 处理单个错误消息
    setErrors([{
      field: 'general',
      message: error.response.data.message
    }])
  }
}
